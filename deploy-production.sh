#!/bin/bash

# JURBOT with Enhanced RAGFlow Service - Production Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="JURBOT Enhanced RAGFlow (Production)"
COMPOSE_FILE="docker-compose.yml"
PROD_COMPOSE_FILE="python-ragflow-service/docker-compose.prod.yml"
ENV_FILE=".env"
PYTHON_ENV_FILE="python-ragflow-service/.env"
PROD_ENV_FILE="python-ragflow-service/production.env"

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}🚀 $1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker found: $(docker --version)"
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_success "Docker Compose found: $(docker-compose --version)"
    
    # Check if jq is installed (for JSON processing)
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed. Some health checks may not work properly."
        print_info "Install with: sudo apt-get install jq"
    else
        print_success "jq found"
    fi
    
    # Check if we're in the right directory
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        print_error "docker-compose.yml not found. Please run this script from the JURBOT root directory."
        exit 1
    fi
    print_success "Found docker-compose.yml"
    
    # Check if production compose file exists
    if [[ ! -f "$PROD_COMPOSE_FILE" ]]; then
        print_error "Production docker-compose.yml not found at $PROD_COMPOSE_FILE"
        exit 1
    fi
    print_success "Found production docker-compose.yml"
    
    # Check if RAGFlow SDK exists
    if [[ ! -d "ragflow/sdk/python" ]]; then
        print_error "RAGFlow Python SDK not found. Please ensure the RAGFlow SDK is available."
        print_info "Expected location: $(pwd)/ragflow/sdk/python"
        print_info "You can clone RAGFlow: git clone https://github.com/infiniflow/ragflow.git"
        print_info "Or keep only the SDK: mkdir -p ragflow/sdk && cp -r /path/to/ragflow/sdk/python ragflow/sdk/"
        exit 1
    fi
    print_success "Found RAGFlow Python SDK"
}

# Function to setup production environment
setup_production_environment() {
    print_header "Setting Up Production Environment"
    
    # Setup production environment file
    if [[ ! -f "$PROD_ENV_FILE" ]]; then
        print_warning "Production environment file not found. Creating from template..."
        cat > "$PROD_ENV_FILE" << EOF
# Production Environment Configuration for Enhanced RAGFlow Service

# Service Configuration
PYTHON_RAGFLOW_SERVICE_PORT=8001
PYTHON_RAGFLOW_SERVICE_HOST=0.0.0.0
LOG_LEVEL=INFO

# Redis Configuration (for session storage)
REDIS_URL=redis://redis:6379/0

# CORS Configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CORS_CREDENTIALS=true

# Performance Configuration
WORKERS=4
RELOAD=false

# RAGFlow Configuration (configure these)
# RAGFLOW_API_KEY=your_ragflow_api_key_here
# RAGFLOW_BASE_URL=http://**************
# RAGFLOW_CHAT_ID=your_chat_id_here
EOF
        print_success "Created production environment file"
    else
        print_success "Found existing production environment file"
    fi
    
    # Check critical production settings
    if grep -q "yourdomain.com" "$PROD_ENV_FILE"; then
        print_warning "Please update CORS_ORIGINS in $PROD_ENV_FILE with your actual domain"
    fi
    
    if grep -q "your_ragflow_api_key_here" "$PROD_ENV_FILE"; then
        print_warning "Please configure RAGFlow settings in $PROD_ENV_FILE"
    fi
}

# Function to create production directories
create_production_directories() {
    print_header "Creating Production Directories"
    
    # Create logs directory
    mkdir -p python-ragflow-service/logs
    chmod 755 python-ragflow-service/logs
    # Set ownership to match Docker container user (UID 1000)
    chown -R 1000:1000 python-ragflow-service/logs 2>/dev/null || true
    print_success "Created logs directory"
    
    # Create SSL directory (if using nginx)
    mkdir -p python-ragflow-service/ssl
    print_success "Created SSL directory"
    
    # Set proper permissions
    chmod 755 python-ragflow-service/logs
    chmod 755 python-ragflow-service/ssl
    print_success "Set directory permissions"
}

# Function to deploy with Redis
deploy_with_redis() {
    print_header "Deploying with Redis Session Storage"
    
    # Stop any existing services
    print_info "Stopping existing services..."
    docker-compose down --remove-orphans || true
    docker-compose -f "$PROD_COMPOSE_FILE" down --remove-orphans || true
    
    # Build and start production services
    print_info "Building and starting production services..."
    docker-compose -f "$PROD_COMPOSE_FILE" build --no-cache
    docker-compose -f "$PROD_COMPOSE_FILE" up -d
    
    print_success "Production services started"
}

# Function to deploy integrated setup
deploy_integrated() {
    print_header "Deploying Integrated Setup"
    
    # Update main docker-compose.yml to use Redis
    print_info "Updating main docker-compose.yml for production..."
    
    # Create a temporary production docker-compose.yml
    cat > docker-compose.prod.yml << EOF
services:
  db:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: \${POSTGRES_USER}
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}
      POSTGRES_DB: \${POSTGRES_DB}
    ports:
      - "5440:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  app:
    build: .
    restart: unless-stopped
    ports:
      - "5173:5173"
      - "5001:5000"
    env_file:
      - ./.env
    environment:
      - DATABASE_URL=postgresql://\${POSTGRES_USER}:\${POSTGRES_PASSWORD}@db:5432/\${POSTGRES_DB}
      - PYTHON_RAGFLOW_SERVICE_URL=http://python-ragflow-service:8001
      - NODE_ENV=production
    depends_on:
      - db
      - python-ragflow-service
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  python-ragflow-service:
    build: ./python-ragflow-service
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - ./ragflow/sdk/python:/app/ragflow_sdk:ro
      - ./python-ragflow-service/logs:/app/logs
    environment:
      - LOG_LEVEL=INFO
      - WORKERS=4
      - RELOAD=false
      - REDIS_URL=redis://redis:6379/0
      - CORS_ORIGINS=\${CORS_ORIGINS:-*}
      - CORS_CREDENTIALS=true
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6381:6379"
    volumes:
      - redis_data:/data
      - ./python-ragflow-service/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

volumes:
  db_data:
  redis_data:
EOF
    
    # Stop existing services
    print_info "Stopping existing services..."
    docker-compose down --remove-orphans || true
    
    # Build and start with production configuration
    print_info "Building and starting production services..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    docker-compose -f docker-compose.prod.yml up -d
    
    print_success "Integrated production deployment completed"
}

# Function to wait for production services
wait_for_production_services() {
    print_header "Waiting for Production Services"
    
    # Wait for Redis
    print_info "Waiting for Redis to be ready..."
    timeout=60
    while ! docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            print_error "Redis failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Redis is ready"
    
    # Wait for database
    print_info "Waiting for database to be ready..."
    timeout=60
    while ! docker-compose -f docker-compose.prod.yml exec -T db pg_isready -U ${POSTGRES_USER:-jurbot} &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            print_error "Database failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Database is ready"
    
    # Wait for Python RAGFlow service
    print_info "Waiting for Python RAGFlow service to be ready..."
    timeout=120
    while ! curl -s -f http://localhost:8001/health/simple &>/dev/null; do
        sleep 3
        timeout=$((timeout - 3))
        if [[ $timeout -le 0 ]]; then
            print_error "Python RAGFlow service failed to start within 120 seconds"
            print_info "Check logs with: docker-compose -f docker-compose.prod.yml logs python-ragflow-service"
            exit 1
        fi
    done
    print_success "Python RAGFlow service is ready"
    
    # Wait for main application
    print_info "Waiting for main application to be ready..."
    timeout=120
    while ! curl -s -f http://localhost:5001/health &>/dev/null && ! curl -s -f http://localhost:5173 &>/dev/null; do
        sleep 3
        timeout=$((timeout - 3))
        if [[ $timeout -le 0 ]]; then
            print_warning "Main application may not be fully ready yet"
            break
        fi
    done
    print_success "Main application is ready"
}

# Function to run production health checks
run_production_health_checks() {
    print_header "Running Production Health Checks"
    
    # Check Redis
    print_info "Checking Redis connection..."
    if docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping | grep -q "PONG"; then
        print_success "Redis is responding"
    else
        print_error "Redis health check failed"
    fi
    
    # Check Python service with Redis
    print_info "Checking Python service with Redis session storage..."
    if curl -s http://localhost:8001/health | jq -e '.sessions.storage_type == "redis"' &>/dev/null; then
        print_success "Python service is using Redis session storage"
    else
        print_warning "Python service may not be using Redis session storage"
    fi
    
    # Check service health
    print_info "Checking overall service health..."
    if curl -s -f http://localhost:8001/health | jq -e '.status == "healthy"' &>/dev/null; then
        print_success "All services are healthy"
    else
        print_warning "Some services may have issues"
        print_info "Check detailed health: curl http://localhost:8001/health | jq"
    fi
}

# Function to show production summary
show_production_summary() {
    print_header "Production Deployment Summary"
    
    echo -e "${GREEN}🎉 Production deployment completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📊 Service URLs:${NC}"
    echo -e "  • Main Application: ${GREEN}http://localhost:5173${NC}"
    echo -e "  • Backend API: ${GREEN}http://localhost:5001${NC}"
    echo -e "  • Python RAGFlow Service: ${GREEN}http://localhost:8001${NC}"
    echo -e "  • Database: ${GREEN}localhost:5440${NC}"
    echo -e "  • Redis: ${GREEN}localhost:6381${NC}"
    echo ""
    echo -e "${BLUE}🔍 Health Checks:${NC}"
    echo -e "  • Service Health: ${GREEN}curl http://localhost:8001/health | jq${NC}"
    echo -e "  • Redis Health: ${GREEN}docker-compose -f docker-compose.prod.yml exec redis redis-cli ping${NC}"
    echo ""
    echo -e "${BLUE}📋 Production Commands:${NC}"
    echo -e "  • View logs: ${YELLOW}docker-compose -f docker-compose.prod.yml logs -f${NC}"
    echo -e "  • Scale Python service: ${YELLOW}docker-compose -f docker-compose.prod.yml up -d --scale python-ragflow-service=3${NC}"
    echo -e "  • Stop services: ${YELLOW}docker-compose -f docker-compose.prod.yml down${NC}"
    echo -e "  • Backup Redis: ${YELLOW}docker-compose -f docker-compose.prod.yml exec redis redis-cli BGSAVE${NC}"
    echo ""
    echo -e "${BLUE}📊 Monitoring:${NC}"
    echo -e "  • Monitor continuously: ${YELLOW}python python-ragflow-service/monitor.py --continuous${NC}"
    echo -e "  • Performance logs: ${YELLOW}tail -f python-ragflow-service/logs/ragflow_performance.log${NC}"
    echo ""
    
    if grep -q "yourdomain.com" "$PROD_ENV_FILE" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Production Configuration Needed:${NC}"
        echo -e "  • Update CORS_ORIGINS in ${YELLOW}$PROD_ENV_FILE${NC}"
        echo -e "  • Configure RAGFlow settings${NC}"
        echo -e "  • Set up SSL certificates (if using nginx)${NC}"
    fi
}

# Main deployment function
main() {
    print_header "$PROJECT_NAME - Automated Deployment"
    
    check_prerequisites
    setup_production_environment
    create_production_directories
    deploy_integrated
    wait_for_production_services
    run_production_health_checks
    show_production_summary
    
    print_success "Production deployment completed successfully! 🎉"
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --logs         Show logs after deployment"
        echo "  --monitoring   Deploy with monitoring (Prometheus + Grafana)"
        echo ""
        echo "This script will deploy JURBOT with Enhanced RAGFlow Service in production mode with:"
        echo "  • Redis session storage"
        echo "  • Resource limits"
        echo "  • Health checks"
        echo "  • Production logging"
        echo "  • Optimized configuration"
        echo ""
        exit 0
        ;;
    --logs)
        main
        echo ""
        print_info "Showing logs (Ctrl+C to exit)..."
        docker-compose -f docker-compose.prod.yml logs -f
        ;;
    --monitoring)
        print_info "Deploying with monitoring enabled..."
        main
        print_info "Starting monitoring services..."
        docker-compose -f python-ragflow-service/docker-compose.prod.yml --profile monitoring up -d
        print_success "Monitoring services started. Grafana: http://localhost:3000 (admin/admin)"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
