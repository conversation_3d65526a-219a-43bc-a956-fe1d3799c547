services:
  db:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5440:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  app:
    build: .
    restart: unless-stopped
    ports:
      - "5173:5173"
      - "5001:5000"
    env_file:
      - ./.env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - PYTHON_RAGFLOW_SERVICE_URL=http://python-ragflow-service:8001
      - NODE_ENV=production
    depends_on:
      - db
      - python-ragflow-service
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  python-ragflow-service:
    build: ./python-ragflow-service
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - ./ragflow/sdk/python:/app/ragflow_sdk:ro
      - ./python-ragflow-service/logs:/app/logs
    environment:
      - LOG_LEVEL=INFO
      - WORKERS=4
      - RELOAD=false
      - REDIS_URL=redis://redis:6379/0
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - CORS_CREDENTIALS=true
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./python-ragflow-service/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

volumes:
  db_data:
  redis_data:
