import os
import json
import asyncio
import logging
import traceback
import time
from typing import Dict, Optional, List, Any
from contextlib import asynccontextmanager

import httpx
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.exception_handlers import http_exception_handler
from pydantic import BaseModel, ValidationError
from sse_starlette.sse import EventSourceResponse
from dotenv import load_dotenv

from ragflow_service import ragflow_service
from session_storage import get_session_storage, close_session_storage

# Load environment variables
load_dotenv()

# Configure enhanced logging
from logging_config import setup_logging, get_logger, perf_logger

log_level = os.getenv("LOG_LEVEL", "INFO").upper()
setup_logging(log_level)
logger = get_logger(__name__)

# Request ID middleware for tracing
class RequestIDMiddleware:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request_id = f"req_{int(time.time() * 1000)}"
            scope["request_id"] = request_id

            # Add request ID to logs
            old_factory = logging.getLogRecordFactory()
            def record_factory(*args, **kwargs):
                record = old_factory(*args, **kwargs)
                record.request_id = request_id
                return record
            logging.setLogRecordFactory(record_factory)

        await self.app(scope, receive, send)

# Global HTTP client
http_client: Optional[httpx.AsyncClient] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global http_client
    http_client = httpx.AsyncClient(timeout=30.0)

    # Initialize session storage
    await get_session_storage()

    logger.info("Python RAGFlow service started")
    yield
    # Shutdown
    if http_client:
        await http_client.aclose()

    # Close session storage
    await close_session_storage()

    logger.info("Python RAGFlow service stopped")

app = FastAPI(
    title="RAGFlow Enhanced Chat Service",
    description="Python FastAPI service for enhanced RAGFlow integration with citations, thinking process, and document references",
    version="1.0.0",
    lifespan=lifespan
)

# Add request ID middleware
app.add_middleware(RequestIDMiddleware)

# CORS middleware
cors_origins = os.getenv("CORS_ORIGINS", "*").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Custom exception handlers
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    # Get the raw request body for debugging
    try:
        body = await request.body()
        raw_data = body.decode('utf-8') if body else "No body"
        logger.error(f"Validation error for request: {raw_data}")
    except Exception as e:
        logger.error(f"Could not read request body: {e}")
        raw_data = "Could not read body"

    logger.error(f"Validation error details: {exc.errors()}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Validation Error",
            "details": exc.errors(),
            "raw_request": raw_data,
            "request_id": getattr(request.scope, "request_id", "unknown")
        }
    )

@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"HTTP exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "request_id": getattr(request.scope, "request_id", "unknown")
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "request_id": getattr(request.scope, "request_id", "unknown")
        }
    )

# Pydantic models
class RAGFlowConfig(BaseModel):
    api_key: str
    base_url: str
    chat_id: str

class ChatRequest(BaseModel):
    message: str
    session_id: str
    ragflow_config: RAGFlowConfig
    stream: bool = True

class ChatResponse(BaseModel):
    content: str
    session_id: Optional[str] = None
    references: Optional[Dict[str, Any]] = None
    thinking_process: Optional[str] = None

# Session storage will be initialized in lifespan

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest, http_request: Request):
    """
    Stream chat response from RAGFlow with enhanced processing using Python SDK
    """
    request_id = getattr(http_request.scope, "request_id", "unknown")
    logger.info(f"[{request_id}] Starting enhanced chat stream for session: {request.session_id}")

    # Validate request
    if not request.message.strip():
        logger.warning(f"[{request_id}] Empty message received")
        raise HTTPException(status_code=400, detail="Message cannot be empty")

    if not request.ragflow_config.api_key:
        logger.warning(f"[{request_id}] Missing RAGFlow API key")
        raise HTTPException(status_code=400, detail="RAGFlow API key is required")

    try:
        async def generate_events():
            event_count = 0
            start_time = time.time()

            try:
                async for event_data in ragflow_service.chat_stream(
                    message=request.message,
                    api_key=request.ragflow_config.api_key,
                    base_url=request.ragflow_config.base_url,
                    chat_id=request.ragflow_config.chat_id,
                    session_id=request.session_id
                ):
                    event_count += 1

                    # Store session mapping if we get a new session ID
                    if event_data.get('session_id') and event_data['session_id'] != request.session_id:
                        storage = await get_session_storage()
                        await storage.set(request.session_id, event_data['session_id'], ttl=86400)  # 24 hours
                        logger.info(f"[{request_id}] Stored session mapping: {request.session_id} -> {event_data['session_id']}")

                    # Add request ID to event data for tracing
                    event_data['request_id'] = request_id

                    yield f"data: {json.dumps(event_data)}\n\n"

                duration = time.time() - start_time
                logger.info(f"[{request_id}] Stream completed: {event_count} events in {duration:.2f}s")

            except Exception as stream_error:
                logger.error(f"[{request_id}] Stream error: {stream_error}", exc_info=True)
                error_event = {
                    "error": str(stream_error),
                    "is_complete": True,
                    "request_id": request_id
                }
                yield f"data: {json.dumps(error_event)}\n\n"

        return EventSourceResponse(generate_events())

    except Exception as e:
        logger.error(f"[{request_id}] Error in chat_stream: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Chat streaming failed: {str(e)}")

@app.post("/chat/complete")
async def chat_complete(request: ChatRequest, http_request: Request) -> ChatResponse:
    """
    Get complete chat response from RAGFlow (non-streaming) using Python SDK
    """
    request_id = getattr(http_request.scope, "request_id", "unknown")
    logger.info(f"[{request_id}] Starting chat completion for session: {request.session_id}")

    # Validate request
    if not request.message.strip():
        logger.warning(f"[{request_id}] Empty message received")
        raise HTTPException(status_code=400, detail="Message cannot be empty")

    if not request.ragflow_config.api_key:
        logger.warning(f"[{request_id}] Missing RAGFlow API key")
        raise HTTPException(status_code=400, detail="RAGFlow API key is required")

    start_time = time.time()

    try:
        result = await ragflow_service.chat_complete(
            message=request.message,
            api_key=request.ragflow_config.api_key,
            base_url=request.ragflow_config.base_url,
            chat_id=request.ragflow_config.chat_id,
            session_id=request.session_id
        )

        # Store session mapping if we get a new session ID
        if result.get('session_id') and result['session_id'] != request.session_id:
            storage = await get_session_storage()
            await storage.set(request.session_id, result['session_id'], ttl=86400)  # 24 hours
            logger.info(f"[{request_id}] Stored session mapping: {request.session_id} -> {result['session_id']}")

        duration = time.time() - start_time
        content_length = len(result.get("content", ""))

        logger.info(f"[{request_id}] Chat completion successful: {content_length} chars in {duration:.2f}s")

        return ChatResponse(
            content=result["content"],
            session_id=result.get("session_id"),
            references=result.get("references", {}),
            thinking_process=result.get("thinking_process")
        )

    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"[{request_id}] Error in chat_complete after {duration:.2f}s: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Chat completion failed: {str(e)}")

@app.post("/debug/validate")
async def debug_validate(request: Request):
    """Debug endpoint to test request validation"""
    try:
        body = await request.body()
        raw_data = body.decode('utf-8') if body else "No body"
        logger.info(f"Debug request received: {raw_data}")

        # Try to parse as JSON
        try:
            json_data = json.loads(raw_data)
            logger.info(f"Parsed JSON: {json_data}")

            # Try to validate as ChatRequest
            chat_request = ChatRequest(**json_data)
            return {
                "status": "success",
                "message": "Request is valid",
                "parsed_data": chat_request.dict(),
                "raw_request": raw_data
            }
        except json.JSONDecodeError as e:
            return {
                "status": "error",
                "message": f"Invalid JSON: {e}",
                "raw_request": raw_data
            }
        except ValidationError as e:
            return {
                "status": "error",
                "message": "Validation failed",
                "errors": e.errors(),
                "raw_request": raw_data
            }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Unexpected error: {e}",
            "raw_request": "Could not read request"
        }

@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint"""
    import time
    import psutil
    from pathlib import Path

    start_time = time.time()

    health_data = {
        "status": "healthy",
        "service": "ragflow-enhanced-chat",
        "version": "1.0.0",
        "timestamp": time.time(),
        "uptime": time.time() - start_time,
        "checks": {}
    }

    # Check HTTP client
    try:
        if http_client:
            health_data["checks"]["http_client"] = "healthy"
        else:
            health_data["checks"]["http_client"] = "not_initialized"
    except Exception as e:
        health_data["checks"]["http_client"] = f"error: {str(e)}"

    # Check RAGFlow SDK availability
    try:
        ragflow_sdk_path = Path(__file__).parent.parent / "ragflow" / "sdk" / "python"
        if ragflow_sdk_path.exists():
            health_data["checks"]["ragflow_sdk"] = "available"
        else:
            health_data["checks"]["ragflow_sdk"] = "not_found"
    except Exception as e:
        health_data["checks"]["ragflow_sdk"] = f"error: {str(e)}"

    # System metrics
    try:
        health_data["system"] = {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent
        }
    except Exception as e:
        health_data["system"] = {"error": str(e)}

    # Session storage stats
    try:
        storage = await get_session_storage()
        session_keys = await storage.keys("*")
        storage_type = "redis" if os.getenv("REDIS_URL") else "in_memory"

        health_data["sessions"] = {
            "active_sessions": len(session_keys),
            "storage_type": storage_type
        }

        health_data["checks"]["session_storage"] = "healthy"
    except Exception as e:
        health_data["checks"]["session_storage"] = f"error: {str(e)}"
        health_data["sessions"] = {
            "active_sessions": 0,
            "storage_type": "unknown",
            "error": str(e)
        }

    # Determine overall status
    failed_checks = [k for k, v in health_data["checks"].items() if "error" in str(v)]
    if failed_checks:
        health_data["status"] = "degraded"
        health_data["failed_checks"] = failed_checks

    return health_data

@app.get("/health/simple")
async def simple_health_check():
    """Simple health check for load balancers"""
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
